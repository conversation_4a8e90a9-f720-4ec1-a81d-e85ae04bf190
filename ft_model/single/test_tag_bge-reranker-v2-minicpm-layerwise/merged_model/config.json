{"architectures": ["LayerWiseMiniCPMForCausalLM"], "attention_bias": false, "attention_dropout": 0.0, "auto_map": {"AutoConfig": "BAAI/bge-reranker-v2-minicpm-layerwise--configuration_minicpm_reranker.LayerWiseMiniCPMConfig", "AutoModel": "BAAI/bge-reranker-v2-minicpm-layerwise--modeling_minicpm_reranker.LayerWiseMiniCPMModel", "AutoModelForCausalLM": "BAAI/bge-reranker-v2-minicpm-layerwise--modeling_minicpm_reranker.LayerWiseMiniCPMForCausalLM"}, "bos_token_id": 1, "dim_model_base": 256, "eos_token_id": 2, "head_multi": true, "head_type": "simple", "hidden_act": "silu", "hidden_size": 2304, "initializer_range": 0.1, "intermediate_size": 5760, "max_position_embeddings": 2048, "model_type": "minicpm", "num_attention_heads": 36, "num_hidden_layers": 40, "num_key_value_heads": 36, "pretraining_tp": 1, "rms_norm_eps": 1e-05, "rope_scaling": null, "rope_theta": 10000.0, "scale_depth": 1.4, "scale_emb": 12, "start_layer": 8, "torch_dtype": "float32", "transformers_version": "4.52.4", "use_cache": false, "vocab_size": 122753}