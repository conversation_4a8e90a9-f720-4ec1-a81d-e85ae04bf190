import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import requests
import threading
import time
import os
from datetime import datetime
import json

class SheetTagGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("文件标签分析工具")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # API服务器配置
        self.api_base_url = "http://221.226.85.235:19999"
        self.current_task_id = None
        self.polling_active = False
        
        # 创建界面
        self.create_widgets()
        
        # 检查API服务器连接
        self.check_server_connection()
    
    def create_widgets(self):
        """创建GUI组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="文件标签分析工具", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 服务器状态
        self.status_frame = ttk.LabelFrame(main_frame, text="服务器状态", padding="5")
        self.status_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.server_status_label = ttk.Label(self.status_frame, text="检查中...", foreground="orange")
        self.server_status_label.grid(row=0, column=0, sticky=tk.W)
        
        self.refresh_btn = ttk.Button(self.status_frame, text="刷新连接", command=self.check_server_connection)
        self.refresh_btn.grid(row=0, column=1, padx=(10, 0))

        self.clear_cache_btn = ttk.Button(self.status_frame, text="清理缓存", command=self.clear_gpu_cache)
        self.clear_cache_btn.grid(row=0, column=2, padx=(10, 0))

        self.memory_btn = ttk.Button(self.status_frame, text="内存信息", command=self.show_memory_info)
        self.memory_btn.grid(row=0, column=3, padx=(10, 0))
        
        # 文件选择区域
        file_frame = ttk.LabelFrame(main_frame, text="文件选择", padding="5")
        file_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        file_frame.columnconfigure(1, weight=1)
        
        ttk.Label(file_frame, text="选择Excel文件:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        
        self.file_path_var = tk.StringVar()
        self.file_entry = ttk.Entry(file_frame, textvariable=self.file_path_var, state="readonly")
        self.file_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 5), pady=(0, 5))
        
        self.browse_btn = ttk.Button(file_frame, text="浏览", command=self.browse_file)
        self.browse_btn.grid(row=0, column=2, pady=(0, 5))
        
        # 工作表名称
        ttk.Label(file_frame, text="工作表名称:").grid(row=1, column=0, sticky=tk.W)
        
        self.worksheet_var = tk.StringVar(value="Sheet1")
        self.worksheet_entry = ttk.Entry(file_frame, textvariable=self.worksheet_var)
        self.worksheet_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(10, 5))
        
        # 操作按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=3, pady=(0, 10))
        
        self.upload_btn = ttk.Button(button_frame, text="开始分析", command=self.start_analysis, state="disabled")
        self.upload_btn.grid(row=0, column=0, padx=(0, 10))
        
        self.cancel_btn = ttk.Button(button_frame, text="取消任务", command=self.cancel_task, state="disabled")
        self.cancel_btn.grid(row=0, column=1, padx=(0, 10))
        
        self.download_btn = ttk.Button(button_frame, text="下载结果", command=self.download_result, state="disabled")
        self.download_btn.grid(row=0, column=2)
        
        # 进度区域
        progress_frame = ttk.LabelFrame(main_frame, text="处理进度", padding="5")
        progress_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        progress_frame.columnconfigure(0, weight=1)
        
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        
        self.progress_label = ttk.Label(progress_frame, text="等待开始...")
        self.progress_label.grid(row=1, column=0, sticky=tk.W)
        
        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="操作日志", padding="5")
        log_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(5, weight=1)
        
        # 创建文本框和滚动条
        self.log_text = tk.Text(log_frame, height=10, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 清除日志按钮
        clear_log_btn = ttk.Button(log_frame, text="清除日志", command=self.clear_log)
        clear_log_btn.grid(row=1, column=0, sticky=tk.W, pady=(5, 0))
    
    def log_message(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def clear_log(self):
        """清除日志"""
        self.log_text.delete(1.0, tk.END)
    
    def check_server_connection(self):
        """检查服务器连接状态"""
        def check():
            try:
                response = requests.get(f"{self.api_base_url}/health", timeout=5)
                if response.status_code == 200:
                    data = response.json()
                    model_status = "已加载" if data.get("model_loaded", False) else "未加载"
                    device = data.get("device", "未知")
                    memory = data.get("memory", {})

                    status_text = f"服务器连接正常 - 模型: {model_status} - 设备: {device}"
                    if memory.get("gpu_total", 0) > 0:
                        gpu_used = memory.get("gpu_allocated", 0)
                        gpu_total = memory.get("gpu_total", 0)
                        status_text += f" - GPU: {gpu_used:.1f}/{gpu_total:.1f}GB"

                    self.server_status_label.config(text=status_text, foreground="green")
                    self.upload_btn.config(state="normal" if self.file_path_var.get() else "disabled")
                    self.log_message("服务器连接成功")
                else:
                    raise Exception(f"服务器响应错误: {response.status_code}")
            except Exception as e:
                self.server_status_label.config(text=f"服务器连接失败: {str(e)}", foreground="red")
                self.upload_btn.config(state="disabled")
                self.log_message(f"服务器连接失败: {str(e)}")

        threading.Thread(target=check, daemon=True).start()

    def clear_gpu_cache(self):
        """清理GPU缓存"""
        def clear():
            try:
                response = requests.post(f"{self.api_base_url}/clear-cache", timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    self.log_message("GPU缓存清理成功")

                    # 更新内存信息
                    memory = data.get("memory", {})
                    if memory.get("gpu_total", 0) > 0:
                        gpu_used = memory.get("gpu_allocated", 0)
                        gpu_total = memory.get("gpu_total", 0)
                        self.log_message(f"GPU内存: {gpu_used:.1f}/{gpu_total:.1f}GB")
                else:
                    self.log_message("清理GPU缓存失败")
            except Exception as e:
                self.log_message(f"清理GPU缓存失败: {str(e)}")

        threading.Thread(target=clear, daemon=True).start()

    def show_memory_info(self):
        """显示内存信息"""
        def get_info():
            try:
                response = requests.get(f"{self.api_base_url}/memory", timeout=5)
                if response.status_code == 200:
                    data = response.json()
                    device = data.get("device", "未知")
                    memory = data.get("memory", {})

                    info_text = f"设备: {device}\n"

                    if memory.get("gpu_total", 0) > 0:
                        gpu_allocated = memory.get("gpu_allocated", 0)
                        gpu_reserved = memory.get("gpu_reserved", 0)
                        gpu_total = memory.get("gpu_total", 0)

                        info_text += f"GPU内存信息:\n"
                        info_text += f"  已分配: {gpu_allocated:.2f} GB\n"
                        info_text += f"  已保留: {gpu_reserved:.2f} GB\n"
                        info_text += f"  总计: {gpu_total:.2f} GB\n"
                        info_text += f"  使用率: {(gpu_allocated/gpu_total)*100:.1f}%"
                    else:
                        info_text += "GPU信息不可用"

                    messagebox.showinfo("内存信息", info_text)
                else:
                    messagebox.showerror("错误", "获取内存信息失败")
            except Exception as e:
                messagebox.showerror("错误", f"获取内存信息失败: {str(e)}")

        threading.Thread(target=get_info, daemon=True).start()
    
    def browse_file(self):
        """浏览文件"""
        file_path = filedialog.askopenfilename(
            title="选择Excel文件",
            filetypes=[("Excel files", "*.xlsx *.xls"), ("All files", "*.*")]
        )
        
        if file_path:
            self.file_path_var.set(file_path)
            self.log_message(f"选择文件: {os.path.basename(file_path)}")
            
            # 如果服务器连接正常，启用上传按钮
            if "连接正常" in self.server_status_label.cget("text"):
                self.upload_btn.config(state="normal")
    
    def start_analysis(self):
        """开始分析"""
        file_path = self.file_path_var.get()
        worksheet_name = self.worksheet_var.get().strip()
        
        if not file_path:
            messagebox.showerror("错误", "请先选择文件")
            return
        
        if not worksheet_name:
            worksheet_name = "Sheet1"
        
        def upload():
            try:
                self.upload_btn.config(state="disabled")
                self.cancel_btn.config(state="normal")
                self.download_btn.config(state="disabled")
                
                self.log_message("开始上传文件...")
                self.progress_label.config(text="上传文件中...")
                
                with open(file_path, 'rb') as f:
                    files = {'file': (os.path.basename(file_path), f, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')}
                    data = {'worksheet_name': worksheet_name}
                    
                    response = requests.post(f"{self.api_base_url}/upload", files=files, data=data, timeout=30)
                
                if response.status_code == 200:
                    result = response.json()
                    self.current_task_id = result['task_id']
                    self.log_message(f"文件上传成功，任务ID: {self.current_task_id}")
                    self.start_polling()
                else:
                    raise Exception(f"上传失败: {response.text}")
                    
            except Exception as e:
                self.log_message(f"上传失败: {str(e)}")
                messagebox.showerror("错误", f"上传失败: {str(e)}")
                self.reset_ui()
        
        threading.Thread(target=upload, daemon=True).start()
    
    def start_polling(self):
        """开始轮询任务状态"""
        self.polling_active = True
        
        def poll():
            while self.polling_active and self.current_task_id:
                try:
                    response = requests.get(f"{self.api_base_url}/status/{self.current_task_id}")
                    if response.status_code == 200:
                        status_data = response.json()
                        
                        # 更新进度条
                        progress = status_data['progress'] * 100
                        self.progress_var.set(progress)
                        self.progress_label.config(text=status_data['message'])
                        
                        if status_data['status'] == 'completed':
                            self.log_message("分析完成！")
                            self.download_btn.config(state="normal")
                            self.polling_active = False
                            self.reset_ui()
                            break
                        elif status_data['status'] == 'failed':
                            error_msg = status_data.get('error', '未知错误')
                            self.log_message(f"分析失败: {error_msg}")
                            messagebox.showerror("错误", f"分析失败: {error_msg}")
                            self.polling_active = False
                            self.reset_ui()
                            break
                    
                    time.sleep(2)  # 每2秒轮询一次
                    
                except Exception as e:
                    self.log_message(f"状态查询失败: {str(e)}")
                    time.sleep(5)  # 出错时等待更长时间
        
        threading.Thread(target=poll, daemon=True).start()
    
    def cancel_task(self):
        """取消任务"""
        if self.current_task_id:
            try:
                response = requests.delete(f"{self.api_base_url}/task/{self.current_task_id}")
                if response.status_code == 200:
                    self.log_message("任务已取消")
                else:
                    self.log_message("取消任务失败")
            except Exception as e:
                self.log_message(f"取消任务失败: {str(e)}")
        
        self.polling_active = False
        self.current_task_id = None
        self.reset_ui()
    
    def download_result(self):
        """下载结果"""
        if not self.current_task_id:
            messagebox.showerror("错误", "没有可下载的结果")
            return
        
        # 选择保存位置
        save_path = filedialog.asksaveasfilename(
            title="保存结果文件",
            defaultextension=".csv",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )
        
        if not save_path:
            return
        
        def download():
            try:
                self.log_message("开始下载结果...")
                response = requests.get(f"{self.api_base_url}/download/{self.current_task_id}")
                
                if response.status_code == 200:
                    with open(save_path, 'wb') as f:
                        f.write(response.content)
                    
                    self.log_message(f"结果已保存到: {save_path}")
                    messagebox.showinfo("成功", f"结果已保存到:\n{save_path}")
                else:
                    raise Exception(f"下载失败: {response.text}")
                    
            except Exception as e:
                self.log_message(f"下载失败: {str(e)}")
                messagebox.showerror("错误", f"下载失败: {str(e)}")
        
        threading.Thread(target=download, daemon=True).start()
    
    def reset_ui(self):
        """重置UI状态"""
        self.upload_btn.config(state="normal" if self.file_path_var.get() and "连接正常" in self.server_status_label.cget("text") else "disabled")
        self.cancel_btn.config(state="disabled")
        self.progress_var.set(0)
        self.progress_label.config(text="等待开始...")

def main():
    root = tk.Tk()
    app = SheetTagGUI(root)
    
    # 设置窗口图标（如果有的话）
    try:
        root.iconbitmap("icon.ico")
    except:
        pass
    
    # 设置窗口关闭事件
    def on_closing():
        if app.polling_active:
            if messagebox.askokcancel("退出", "正在处理任务，确定要退出吗？"):
                app.polling_active = False
                root.destroy()
        else:
            root.destroy()
    
    root.protocol("WM_DELETE_WINDOW", on_closing)
    
    # 启动GUI
    root.mainloop()

if __name__ == "__main__":
    main()
