# Excel标签预测处理说明

## 功能概述

本次修改实现了以下功能：
1. 读取Excel文件中每一行的T列和U列值，并将它们拼接作为context_value
2. 对拼接后的内容进行标签推理
3. 将推理得到的标签结果输出到同一Excel文件的V列
4. 输出结果为Excel格式，方便业务人员下载使用

## 主要修改

### 1. 新增函数 `get_testing_set_with_tu_columns`

```python
def get_testing_set_with_tu_columns(file_path, worksheet_list):
    """
    读取Excel文件，取T列和U列拼接作为context_value
    
    Args:
        file_path: Excel文件路径
        worksheet_list: 工作表名称列表
    
    Returns:
        results: 包含(行号, 拼接后的context, 原始T列值, 原始U列值)的列表
    """
```

**功能说明：**
- 读取指定工作表的T列（第20列）和U列（第21列）
- 将T列和U列的值拼接作为context
- 返回行号信息以便后续写回结果

### 2. 新增函数 `t4gger4040_excel`

```python
def t4gger4040_excel(file_path, worksheet_list, output_name):
    """
    新版本的标签预测函数，读取T列和U列拼接作为context，将结果写入V列
    
    Args:
        file_path: Excel文件路径
        worksheet_list: 工作表名称列表
        output_name: 输出文件名（不含扩展名）
    """
```

**功能说明：**
- 使用新的读取函数获取T列和U列拼接的数据
- 对每行数据进行标签推理
- 将最佳预测结果写入V列（第22列）
- 保存为Excel格式文件

## 使用方法

### 1. 直接调用函数

```python
from sheetTag import t4gger4040_excel

# 配置参数
file_path = "your_input_file.xlsx"  # 您的Excel文件路径
worksheet_list = ["Sheet1", "Sheet2"]  # 要处理的工作表名称列表
output_name = "processed_results"  # 输出文件名

# 执行处理
t4gger4040_excel(file_path, worksheet_list, output_name)
```

### 2. 使用测试脚本

运行提供的测试脚本：

```bash
python test_excel_processing.py
```

记得修改脚本中的文件路径和工作表名称。

## 输入要求

1. **Excel文件格式**：支持.xlsx格式
2. **数据位置**：
   - T列（第20列）：包含第一部分内容
   - U列（第21列）：包含第二部分内容
3. **数据从第2行开始**：第1行通常是标题行

## 输出结果

1. **输出文件**：保存在`./output/`目录下，文件名为`{output_name}.xlsx`
2. **结果位置**：V列（第22列）包含推理得到的标签
3. **文件格式**：Excel格式，保持原文件的所有其他数据不变

## 注意事项

1. 确保`./output/`目录存在，如果不存在请先创建
2. 确保有足够的磁盘空间保存输出文件
3. 处理大文件时可能需要较长时间，请耐心等待
4. 如果T列或U列为空，该行会被跳过处理
5. 确保模型文件和数据库文件已正确配置

## 错误处理

如果遇到错误，请检查：
1. 文件路径是否正确
2. 工作表名称是否存在
3. Excel文件是否已被其他程序占用
4. 模型和数据库文件是否可访问

## 性能说明

- 处理速度取决于数据量和模型推理速度
- 建议分批处理大量数据
- 程序会在控制台输出处理进度信息
