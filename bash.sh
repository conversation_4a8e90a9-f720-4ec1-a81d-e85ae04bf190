#./bash.sh ./output/APP运营室-APP功能-电子发票_training.jsonl 1516
#./bash.sh ./output/生产交付室-号卡业务-无忧卡_training.jsonl 6460
#./bash.sh ./output/ALL_training.jsonl 77320 98 2141 32
#cat ./output/生产交付室-号卡业务-无忧卡_training_single.jsonl ./output/APP运营室-APP功能-电子发票_training_single.jsonl > ./merged.jsonl

# 通信优化
#export NCCL_IGNORE_CPU_AFFINITY=1      # 避免自动CPU绑定冲突
#export NCCL_P2P_LEVEL=NVL              # 强制NVLink
#export NCCL_NVLS_ENABLE=1              # 启用NVLink SHARP
#export NCCL_NET_GDR_READ=1             # GPU直读
#export NCCL_P2P_DISABLE=0              # 启用P2P
#export NCCL_SHM_DISABLE=1              # 禁用共享内存
#export NCCL_NET_GDR_LEVEL=3            # 允许访问远程内存
#export NCCL_SOCKET_IFNAME='eth0'       # 指定网卡
#export NCCL_SHM_USE_CUDA_MEMCPY=1
# 内存分配优化
export PYTORCH_CUDA_ALLOC_CONF="backend:cudaMallocAsync,garbage_collection_threshold:0.8,max_split_size_mb:128"
export PYTORCH_CUDA_ALLOC_DEBUG=backend
export CUDA_LAUNCH_BLOCKING=1
# export CUDACXX=/usr/local/cuda/bin/nvcc

export WANDB_MODE=disabled
export HF_ENDPOINT=https://hf-mirror.com
export HF_HUB_CACHE="./.cache/huggingface/hub"

train_data=$1
max_steps=$2
train_group_size=$3
query_max_len=$4
passage_max_len=$5

num_gpus=2
num_train_epochs=2
per_device_train_batch_size=1
gradient_accumulation_steps=2

cmd_new="torchrun --nproc_per_node $num_gpus \
	-m FlagEmbedding.finetune.reranker.decoder_only.layerwise \
    --model_name_or_path BAAI/bge-reranker-v2-minicpm-layerwise \
    --use_lora True \
    --lora_rank 32 \
    --lora_alpha 64 \
    --use_flash_attn True \
    --target_modules q_proj k_proj v_proj o_proj \
    --save_merged_lora_model True \
    --model_type decoder \
    --model_type from_finetuned_model \
    --start_layer 8 \
    --head_multi True \
    --head_type simple \
    --trust_remote_code True \
    --cache_dir $HF_HUB_CACHE \
    --train_data $train_data \
    --cache_path ./cache/data \
    --train_group_size $train_group_size \
    --query_max_len $query_max_len \
    --passage_max_len $passage_max_len \
    --pad_to_multiple_of 8 \
    --knowledge_distillation False \
    --query_instruction_for_rerank '工单内容：' \
    --query_instruction_format '{}{}' \
    --passage_instruction_for_rerank '标签：' \
    --passage_instruction_format '{}{}' \
	--output_dir ./test_tag_bge-reranker-v2-minicpm-layerwise \
    --overwrite_output_dir \
    --learning_rate 2e-5 \
    --bf16 \
    --num_train_epochs $num_train_epochs \
    --per_device_train_batch_size $per_device_train_batch_size \
    --gradient_accumulation_steps $gradient_accumulation_steps \
    --max_steps $max_steps \
    --dataloader_drop_last True \
    --warmup_ratio 0.1 \
    --gradient_checkpointing \
    --weight_decay 0.01 \
    --deepspeed ./ds_stage1.json \
    --logging_steps 1 \
    --save_steps 10000
"

echo $cmd_new
eval $cmd_new